<script lang="ts">
import {T} from "@threlte/core";
import * as THREE from "three";

let {
    url,
    position = [0, 0, 0],
}: {
    url: string;
    position?: [number, number, number];
} = $props();

let texture = $state<THREE.Texture | null>(null);

const loadTexture = (url: string) => {
    return new Promise<THREE.Texture>((resolve, reject) => {
        const loader = new THREE.TextureLoader();

        loader.load(
            url,
            texture => {
                if (texture === null) return;
                
                texture.colorSpace = THREE.SRGBColorSpace;
                resolve(texture);
            },
            undefined,
            error => {
                console.error("Failed to load texture:", error);
                reject(error);
            },
        );
    });
}

// Load texture when component mounts or URL changes
$effect(() => {
    loadTexture(url).then((loadedTexture) => {
        texture = loadedTexture;
    });
});
</script>

{#if texture !== null}
    <T.Mesh {position}>
        <T.PlaneGeometry args={[texture.width / texture.height, 1]} />
        <T.MeshStandardMaterial
            map={texture}
            side={THREE.DoubleSide}
            castShadow
        />
    </T.Mesh>
{/if} 